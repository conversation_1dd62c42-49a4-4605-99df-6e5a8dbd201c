<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>

<script setup lang="ts" name="PAGE_308">
import { searchFormSchema, transformTableData, transformTableDataToArray } from './datas/datas'
import { GetBusinessDataAnalysis } from '/@/api/newReportForm/operationdataanalysis'
import { BasicTable, useTable } from '/@/components/Table'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'
import { storeToRefs } from 'pinia'
import { watch, nextTick } from 'vue'

const profitPotentialStore = useProfitPotentialStore()
const { toProductOperationParams } = storeToRefs(profitPotentialStore)

watch(
  () => toProductOperationParams.value,
  (newVal: any) => {
    if (newVal) {
      console.log(newVal)

      nextTick(async () => {
        const formInstance = await getForm() // 确保在异步回调中获取表单实例
        if (formInstance) {
          const { startDate, endDate, no_cache } = newVal
          const newFormData = { no_cache, start_date: startDate, end_date: endDate, dept_ids: newVal.department }
          await getForm().setFieldsValue(newFormData)
          setTimeout(async () => {
            const params = await handleBeforeFetch(newFormData)

            reload(params)
          })
        }
      })
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const [registerTable, { setColumns, getForm, reload }] = useTable({
  showIndexColumn: false,
  showTableSetting: true,
  useSearchForm: true,
  api: GetBusinessDataAnalysis,
  formConfig: {
    actionColOptions: {
      span: 24
    },
    labelWidth: 120,
    baseColProps: { span: 6 },
    schemas: searchFormSchema,
    fieldMapToTime: [['date', ['start_date', 'end_date'], ['YYYY-MM-DD', 'YYYY-MM-DD']]]
  },
  pagination: {
    pageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
    position: ['bottomRight']
  },
  beforeFetch: handleBeforeFetch,
  afterFetch: handleAfterFetch
})

function handleBeforeFetch(params) {
  console.log(params)
  const dempents = params.dept_ids || params.operation
  return {
    ...params,
    dept_ids: void 0,
    operation: dempents ? dempents.map((item) => item.value) : void 0
  }
}

function handleAfterFetch(data) {
  const newcolumns = transformTableDataToArray(data)
  const newdata = transformTableData(data)
  setColumns(newcolumns)
  return newdata
}
</script>
