// 金蝶
import { RecordItem } from './model/kingDeeModel'
import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetKDPageList = '/stb/kd/get',
  ImportKDRecords = '/stb/kd/importData',
  GetKingToWell = '/stb/kd/changeData'
}

export const getKDListByPage = (params?: {} & BasicPageParams) =>
  defHttp.get<BasicFetchResult<RecordItem>>({ url: Api.GetKDPageList, params })

//金蝶上传接口
export const importKDRecords = (params) => defHttp.post({ url: Api.ImportKDRecords, params }, { successMessageMode: 'message' })

//金蝶转万维
export const getKingToWell = (params?: { year?: number; issue?: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetKingToWell, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
