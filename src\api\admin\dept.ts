import { DeptItem } from './model/deptModel'

import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetDeptTree = '/department/getTree',
  GetDeptSelectTree = '/department/getSelectTree',
  CreateDeptItem = '/department/create',
  UpdateDeptItem = '/department/update',
  GetDeptItemDetail = '/department/detail',
  DeleteDeptItem = '/department/delete'
}

export const getDeptTree = (params?: { status?: number; isHidden?: number }) => defHttp.get<DeptItem[]>({ url: Api.GetDeptTree, params })

export const getDeptSelectTree = (params?: {}) => defHttp.get<DeptItem[]>({ url: Api.GetDeptSelectTree, params })

export const createDeptItem = (params?: DeptItem) =>
  defHttp.post<{ id: number }>({ url: Api.CreateDeptItem, params }, { successMessageMode: 'message' })

export const updateDeptItem = (params?: DeptItem) =>
  defHttp.post<{ id: number }>({ url: Api.UpdateDeptItem, params }, { successMessageMode: 'message' })

export const getDeptItemDetail = (params?: { id: number; type?: number }) => defHttp.get<DeptItem>({ url: Api.GetDeptItemDetail, params })

export const deleteDeptItem = (params?: { id: number }) =>
  defHttp.get<{ id: number }>({ url: Api.DeleteDeptItem, params }, { successMessageMode: 'message' })
