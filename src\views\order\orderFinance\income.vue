<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" @click="handleExport" :loading="exportLoading" :disabled="exportLoading">
          <download-outlined />
          根据搜索条件导出文件
        </a-button>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="PAGE_301">
import { useTable, BasicTable } from '/@/components/Table'
import { getSchema, incomeColumns } from './datas/data'
import { GetExportOrderIncome, getOrderIncome } from '/@/api/order/income'
import { nextTick, ref } from 'vue'
import { downloadByData } from '/@/utils/file/download'

const exportLoading = ref(false)

const [registerTable, { getForm, getRawDataSource, setLoading }] = useTable({
  title: '订单收入',
  showTableSetting: true,
  columns: incomeColumns,
  api: getOrderIncome,
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  useSearchForm: true,
  immediate: false,
  formConfig: {
    labelWidth: 150,
    schemas: getSchema('/reportForm/income'),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['date', ['start_date', 'end_date'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  showSummary: true,
  summaryFunc: () => {
    nextTick()
    const rawDataSource = getRawDataSource()
    return [
      {
        dept_name: '汇总',
        amount: rawDataSource.total_amount
      }
    ]
  },
  beforeFetch: (params) => {
    const { operation_ids, department_ids, operation_id, department_id } = params

    return {
      ...params,
      operation_id: operation_ids ? operation_ids.map((item) => item.value) : operation_id,
      department_id: department_ids ? department_ids.map((item) => item.value) : department_id,
      operation_ids: void 0,
      department_ids: void 0
    }
  }
})

async function handleExport() {
  try {
    setLoading(true)
    exportLoading.value = true

    const cloneParams = await getForm().getFieldsValue()

    const res = await GetExportOrderIncome(cloneParams)

    downloadByData(res as any, `FIMS-订单收入列表导出文件-${Date.now()}.xlsx`)
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
    exportLoading.value = false
  }
}
</script>
