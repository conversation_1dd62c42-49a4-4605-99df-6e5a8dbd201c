import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetAllDeptWithoutAuth = '/deptgroup/getDepartmentList',
  GetDepartmentTree = '/deptgroup/getDepartmentTree',
  GetDepartmentPermissionTree = '/deptgroup/getDepartmentPermissionTree'
}

//获取所有部门(没权限控制的平级)
export const getAllDeptWithoutAuth = (params?) =>
  defHttp.get({ url: Api.GetAllDeptWithoutAuth, params }).then(({ items }) => {
    return {
      items: items.map((item) => {
        return { ...item, key: item.id, label: item.name, pId: item.parentId }
      })
    }
  })

//获取所有部门(有权限控制的平级)可以传入比如{key:'is_product'}来过滤
export const getDeptWithoutAuthFromParams = (params) =>
  defHttp
    .get({
      url: Api.GetAllDeptWithoutAuth
    })
    .then((res) => {
      return {
        items: Object.values(res.items)
          .map((item: Recordable) => {
            return { ...item, key: item.id, label: item.name }
          })
          .filter((item) => {
            return item[params.key] === 1
          })
      }
    })

//获取所有部门(树形部门)
export const getDepartmentTree = (params?) => defHttp.get({ url: Api.GetDepartmentTree, params })

//获取授权部门树
export const getDepartmentPermissionTree = (params?) => defHttp.get({ url: Api.GetDepartmentPermissionTree, params })
