<template>
  <div>
    <BasicTable @register="registerTable" :loading="loading">
      <template #form-advanceBefore v-if="hasPermission([312])">
        <a-button type="primary" @click="handleExport" :loading="exportLoading" :disabled="exportLoading">
          <download-outlined />
          根据搜索条件导出文件
        </a-button>
      </template>
    </BasicTable>
    <!-- <DetailsModal @register="registerModal" /> -->
  </div>
</template>
<script lang="ts" setup name="PAGE_306">
import { useTable, BasicTable } from '/@/components/Table'
import { getColumns, schema, searchInfo } from './datas/data'
import dayjs from 'dayjs'
import { getexportFeeStatic, getFeeStatic } from '/@/api/order/feeStatic'
import { nextTick, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
// import { useModal } from '/@/components/Modal'
// import DetailsModal from './components/DetailsModal.vue'
import { cloneDeep } from 'lodash-es'
import { storeToRefs } from 'pinia'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'
import { downloadByData } from '/@/utils/file/download'
import { usePermission } from '/@/hooks/web/usePermission'

const route = useRoute()
// const [registerModal, { openModal, setModalProps }] = useModal()
const loading = ref(true)
const exportLoading = ref(false)

const profitPotentialStore = useProfitPotentialStore()
const { hasPermission } = usePermission()

const { tofeestaticParams } = storeToRefs(profitPotentialStore)

watch(
  () => tofeestaticParams.value,
  (newVal: any) => {
    if (newVal) {
      nextTick(async () => {
        const formInstance = await getForm() // 确保在异步回调中获取表单实例
        if (formInstance) {
          const { group_by, department, category1 } = newVal
          const newFormData = { group_by, department, category1 }
          await getForm().setFieldsValue(newFormData)
          setTimeout(async () => {
            const params = await handleBeforeFetch(newFormData)

            reload(params)
          })
        }
      })
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const [registerTable, { setColumns, reload, getForm, setLoading }] = useTable({
  api: getFeeStatic,
  showTableSetting: true,
  columns: [],
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  immediate: false,
  searchInfo: {
    category1: '营业费用',
    group_by: 'category'
  },
  title: '费用汇总（部门负责人看板）',
  formConfig: {
    labelWidth: 150,
    schemas: cloneDeep(schema),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  beforeFetch: handleBeforeFetch
})

function handleBeforeFetch(params) {
  searchInfo.value = {
    ...params,
    year: dayjs(params.year).format('YYYY')
  }
  return searchInfo.value
}

onMounted(async () => {
  // 初始化表格
  await setColumns(getColumns(route.path))

  reload()
})

async function handleExport() {
  try {
    setLoading(true)
    exportLoading.value = true

    const cloneParams = await getForm().getFieldsValue()

    const params = await handleBeforeFetch(cloneParams)

    const res = await getexportFeeStatic({ ...params, category1: '营业费用', group_by: 'category' })

    downloadByData(res as any, `FIMS-费用汇总凭证(科目)列表导出文件-${Date.now()}.xlsx`)
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
    exportLoading.value = false
  }
}
</script>
