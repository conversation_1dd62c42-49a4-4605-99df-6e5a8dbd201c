// 凭证批号
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetAnywellRecordBatchNo = '/stb/rb/get',
  DeleteAnywellRecordBatchNo = '/stb/rb/remove',
  GetKingDeeRecordBatchNo = '/stb/rb/getKd',
  DeleteKingDeeRecordBatchNo = '/stb/rb/removeKd'
}

export const getAnywellRecordBatchNo = (params?: {}) => defHttp.get({ url: Api.GetAnywellRecordBatchNo, params })

export const deleteAnywellRecordBatchNo = (params?: { year: number; issue: number; batch_no: string; created_at: string }) =>
  defHttp.get({ url: Api.DeleteAnywellRecordBatchNo, params })

export const getKingDeeRecordBatchNo = (params?: {}) => defHttp.get({ url: Api.GetKingDeeRecordBatchNo, params })

export const deleteKingDeeRecordBatchNo = (params?: { act_date: string; batch_no: string; created_at: string }) =>
  defHttp.get({ url: Api.DeleteKingDeeRecordBatchNo, params })
