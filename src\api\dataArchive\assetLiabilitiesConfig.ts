import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetAssetLiabilitiesConfig = '/al/getConfigTree',
  UpdateAssetLiabilitiesConfig = '/al/upsertConfig',
  DeleteAssetLiabilitiesConfig = '/al/deleteConfig'
}
export function getAssetLiabilitiesConfigList(params) {
  return defHttp.get({ url: Api.GetAssetLiabilitiesConfig, params })
}

export function updateAssetLiabilitiesConfig(data) {
  return defHttp.post({ url: Api.UpdateAssetLiabilitiesConfig, data })
}

export function deleteAssetLiabilitiesConfig(data) {
  return defHttp.post({ url: Api.DeleteAssetLiabilitiesConfig, data })
}
