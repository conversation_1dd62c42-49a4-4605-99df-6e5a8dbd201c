import { h } from 'vue'
import { mapRouterPath } from '../../operationdataanalysis/datas/datas'
import { getDepartmentPermissionTree } from '/@/api/common/dept'
import { FormSchema } from '/@/components/Form'
import { ACCOUNT_PAYABLE, ACCOUNT_RECEIVABLE, PROFIT } from '/@/const/skipPathMap'
import { useERPStore } from '/@/store/modules/ERP'
import { BasicTitle } from '/@/components/Basic'
import { useGo } from '/@/hooks/web/usePage'
import { router } from '/@/router'

const erpStore = useERPStore()
const go = useGo(router)

export const searchFormSchema: FormSchema[] = [
  {
    field: 'date',
    label: '日期',
    component: 'RangePicker',
    // required: !hasPermission([288]),
    defaultValue: erpStore.dateRangeArray,
    componentProps: {
      style: { width: '100%' },
      disabledDate: erpStore.disabledDate
    },
    colProps: { span: 8 }
  },
  {
    field: 'no_cache',
    label: '重新计算当月报表',
    component: 'Select',
    defaultValue: 0,
    componentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    },
    colProps: { span: 8 }
  },
  {
    field: `dept_ids`,
    label: `部门`,
    component: 'ApiTreeSelect',
    // required: !hasPermission([92]),
    componentProps: ({ formActionType }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      labelInValue: true,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },

        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      },
      onChange: () => {
        if (formActionType && formActionType.getFieldsValue().operation) {
          formActionType.setFieldsValue({
            operation: undefined
          })
        }
      }
    }),
    colProps: { span: 24 }
  },
  {
    field: `operation`,
    label: `部门(不筛选子部门)`,
    component: 'ApiTreeSelect',
    colProps: { span: 24 },
    componentProps: ({ formActionType }) => ({
      resultField: 'items',
      immediate: false,
      lazyLoad: true,
      labelInValue: true,
      api: getDepartmentPermissionTree,
      treeSelectProps: {
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        multiple: true,
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },

        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      },
      onChange: () => {
        if (formActionType && formActionType.getFieldsValue().dept_ids) {
          formActionType.setFieldsValue({
            dept_ids: undefined
          })
        }
      }
    })
  }
]

export function transformTableDataToArray(tableData: Record<string, any[]>) {
  const tableTitles = Object.keys(tableData)
  const newcolumns = tableTitles.map((item) => {
    return {
      title: item,
      dataIndex: item
    }
  })
  Object.entries(tableData).forEach(([baseTitle, items]) => {
    newcolumns.forEach((item: any) => {
      if (item.title == baseTitle) {
        const newarr = items[0].map((val) => {
          if (val === '内容') {
            return {
              title: val,
              dataIndex: `${baseTitle}-${val}`,
              width: 120,
              resizable: true,
              customRender: ({ record }) => {
                const content = record[`${baseTitle}-${val}`]
                // 检查是否在路由映射中或是特殊情况
                let isRouter = Object.keys(mapRouterPath).includes(content)
                let routePath = ''
                // 处理特殊情况: 应收账款、应付账款和业绩，只要包含这些字就需要跳转
                if (!isRouter) {
                  if (content && typeof content === 'string') {
                    if (content.includes(ACCOUNT_RECEIVABLE)) {
                      isRouter = true
                      routePath = mapRouterPath[ACCOUNT_RECEIVABLE]
                    } else if (content.includes(ACCOUNT_PAYABLE)) {
                      isRouter = true
                      routePath = mapRouterPath[ACCOUNT_PAYABLE]
                    } else if (content.includes(PROFIT)) {
                      isRouter = true
                      routePath = mapRouterPath[PROFIT]
                    }
                  }
                }
                return h(
                  BasicTitle,
                  {
                    style: `color:${isRouter ? '#40a9ff' : ''};margin-right:10px;`,
                    onClick: () => {
                      if (isRouter) {
                        // 使用特殊处理得到的路径或默认路径
                        const path = routePath || mapRouterPath[content]
                        if (path) {
                          go({
                            path: path,
                            state: {
                              searchParams: { condition2: 1 }
                            }
                          })
                        }
                      }
                    }
                  },
                  () => content
                )
              }
            }
          } else {
            return {
              title: val,
              dataIndex: `${baseTitle}-${val}`,
              width: 120,
              resizable: true
            }
          }
        })
        item.children = newarr
      }
    })
  })

  return newcolumns
}
export function transformTableData(data: Record<string, any[]>) {
  const mergedData: any[] = []

  Object.entries(data).forEach(([key, val]) => {
    const headers = val[0].map((item) => `${key}-${item}`) // 获取表头
    const dataRows = val.slice(1) // 获取数据行

    const tableData = dataRows.map((row) => {
      const rowObject: Record<string, any> = {}
      headers.forEach((header, index) => {
        rowObject[header] = row[index]
      })
      return rowObject
    })

    tableData.forEach((row, rowIndex) => {
      // 初始化或获取对应索引位置的合并对象
      if (!mergedData[rowIndex]) {
        mergedData[rowIndex] = {}
      }
      // 将当前行的数据合并到对应索引位置的对象中
      headers.forEach((header) => {
        mergedData[rowIndex][header] = row[header]
      })
    })
    console.log(tableData)
  })
  return mergedData
}
