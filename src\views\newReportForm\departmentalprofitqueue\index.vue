<template>
  <div>
    <BasicTable @register="registerTable">
      <template #toolbar>
        <a-button type="primary" @click="handleadd">新增</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'"> <TableAction :actions="createActions(record)" /> </template>
      </template>
    </BasicTable>
    <workDrawer @register="registerDrawer" @success="reload" />
  </div>
</template>
<script setup lang="ts">
import { GetDepartmentProfitSimQueue } from '/@/api/newReportForm/divisionalprofit'
import { BasicTable, useTable, TableAction, ActionItem } from '/@/components/Table'
import workDrawer from './components/workDrawer.vue'
import { useDrawer } from '/@/components/Drawer'
import { onBeforeUnmount, onMounted, onUnmounted } from 'vue'

const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

const [registerTable, { reload }] = useTable({
  useSearchForm: false,
  showIndexColumn: false,
  showTableSetting: true,
  api: GetDepartmentProfitSimQueue,
  columns: [
    { title: '计算时间与部门', dataIndex: 'input_text', resizable: true },
    { title: '状态', dataIndex: 'status', resizable: true },
    { title: '创建日期', dataIndex: 'created_at', resizable: true },
    { title: '计算耗时', dataIndex: 'duration', resizable: true }
  ],
  actionColumn: {
    width: 300,
    title: '操作',
    dataIndex: 'action'
  }
})

function createActions(record: any): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '查看',
      onClick: handleDetail.bind(null, record),
      disabled: record.status !== '完成'
    }
  ]
}

function handleadd() {
  openDrawer(true, {
    type: 'add'
  })
  setDrawerProps({
    width: '40%',
    showFooter: true,
    title: '汇总表单'
  })
}
function handleDetail(record: any) {
  openDrawer(true, {
    record,
    type: 'detail'
  })
  setDrawerProps({
    width: '90%',
    showFooter: true,
    title: '产品与项目经营数据汇总'
  })
}

// 定义轮询定时器和间隔时间
let pollTimer: ReturnType<typeof setInterval> | null = null
const POLL_INTERVAL = 60000 // 1分钟秒轮询一次

// 启动轮询函数
function startPolling() {
  // 清除可能存在的旧定时器
  stopPolling()

  // 设置新的定时器
  pollTimer = setInterval(() => {
    reload()
  }, POLL_INTERVAL)
}

// 停止轮询函数
function stopPolling() {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null
  }
}

// 组件挂载时启动轮询
onMounted(() => {
  startPolling()
})

// 组件卸载前进行清理工作
onBeforeUnmount(() => {
  // 停止轮询
  stopPolling()
  // 清理其他资源或引用
  console.log('组件即将销毁，清理资源')
})

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling()
  console.log('组件已卸载')
})
</script>
