import { Random } from 'mockjs'
import { MockMethod } from 'vite-plugin-mock'
import { resultError, resultPageSuccess, resultSuccess } from '../_util'

const menuList = (() => {
  const result: any[] = []
  for (let index = 0; index < 3; index++) {
    result.push({
      // id: '@integer(1000, 9999)',
      id: index,
      key: index, //在selectList中冗余的
      type: 0,
      menuName: ['Dashboard', '权限管理', '功能'][index],
      title: ['Dashboard', '权限管理', '功能'][index], //在selectList中冗余的
      icon: ['ion:layers-outline', 'ion:git-compare-outline', 'ion:tv-outline'][index],
      routePath: ['/dashboard', '/admin', '/function'][index],
      component: 'LAYOUT',
      orderNo: index + 1,
      'isHidden|1': [0, 0, 0],
      'status|1': [0, 0, 1],
      children: (() => {
        const children: any[] = []
        for (let j = 0; j < 4; j++) {
          children.push({
            id: '@integer(1000, 9999)',
            key: '@integer(1000, 9999)', //在selectList中冗余的
            type: 1,
            menuName: ['页面1', '页面2', '页面3', '页面4'][j],
            title: ['页面1', '页面2', '页面3', '页面4'][j],
            icon: 'ion:document',
            routePath: ['/menu', '/menu2', '/menu3', '/menu4'][index],
            component: ['/dashboard/welcome/index', '/dashboard/analysis/index', '/dashboard/workbench/index', '/dashboard/test/index'][j],
            orderNo: j + 1,
            'isHidden|1': [1, 0, 0],
            'status|1': [0, 1],
            children: (() => {
              const children: any[] = []
              for (let k = 0; k < 4; k++) {
                children.push({
                  id: '@integer(1000, 9999)',
                  key: '@integer(1000, 9999)',
                  type: 2,
                  menuName: '按钮' + (j + 1) + '-' + (k + 1),
                  title: '按钮' + (j + 1) + '-' + (k + 1),
                  icon: '',
                  routePath: null,
                  component: [
                    '/dashboard/welcome/index',
                    '/dashboard/analysis/index',
                    '/dashboard/workbench/index',
                    '/dashboard/test/index'
                  ][j],
                  orderNo: j + 1,
                  'isHidden|1': [0, 1, 0],
                  'status|1': [0, 1],
                  children: undefined,
                  createTime: '@datetime'
                })
              }
              return children
            })(),
            createTime: '@datetime'
          })
        }
        return children
      })(),
      createTime: '@datetime'
    })
  }
  return result
})()

const getMenuItemDetail = (type) => {
  const result = [
    {
      id: '@integer(1000, 9999)',
      parentId: 0,
      type: 0,
      menuName: '某个菜单',
      icon: ['ion:layers-outline', 'ion:git-compare-outline', 'ion:tv-outline'][Random.integer(0, 2)],
      orderNo: 1,
      'isExt|1': [1, 0, 0],
      'isHidden|1': [0, 1, 0],
      'noCache|1': [0, 0, 1],
      routePath: ['/dashboard', '/admin', '/funtion'][Random.integer(0, 2)],
      component: 'LAYOIUT',
      api: '@url',
      'status|1': [0, 1],
      createTime: '@datetime'
    },
    {
      id: '@integer(1000, 9999)',
      parentId: '@integer(1000, 9999)',
      type: 1,
      menuName: '某个页面',
      icon: ' ion:document',
      orderNo: 1,
      'isExt|1': [1, 0, 0],
      'isHidden|1': [0, 1, 0],
      routePath: 'menu1',
      component: ['/dashboard/welcome/index', '/dashboard/analysis/index', '/dashboard/workbench/index', '/dashboard/test/index'][
        Random.integer(0, 3)
      ],
      api: '@url',
      'status|1': [0, 1],
      createTime: '@datetime'
    },
    {
      id: '@integer(1000, 9999)',
      parentId: '@integer(1000, 9999)',
      type: 2,
      menuName: '某个部件',
      icon: null,
      orderNo: 1,
      'isExt|1': [1, 0, 0],
      'isHidden|1': [0, 1, 0],
      routePath: null,
      component: null,
      api: '@url',
      'status|1': [0, 1],
      createTime: '@datetime'
    }
  ][type]

  return result
}

export default [
  {
    url: '/api/menu/detail',
    timeout: 100,
    method: 'get',
    response: (params) => {
      return resultSuccess(getMenuItemDetail(params.query.type))
    }
  },
  {
    url: '/api/menu/create',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess({
        id: Random.integer(1000, 9999)
      })
    }
  },
  {
    url: '/api/menu/update',
    timeout: 100,
    method: 'post',
    response: () => {
      return resultSuccess({
        id: Random.integer(1000, 9999)
      })
    }
  },
  {
    url: '/api/menu/getTree',
    timeout: 100,
    method: 'get',
    response: () => {
      return resultSuccess(menuList)
    }
  },
  {
    url: '/api/menu/getSelectTree',
    timeout: 100,
    method: 'get',
    response: () => {
      return resultSuccess(menuList)
    }
  }
] as MockMethod[]
