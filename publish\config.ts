const Utils = require('./utils.ts')

const baseConfig = {
  host: '**************',
  port: 22,
  username: 'moxuxian',
  privateKey: Utils.getSSHPrivateKey()
}

module.exports = function (mode) {
  const mapRemote = {
    tests: '/www/wwwroot/fims.gbuilderchina.com/tests',
    prod: '/www/wwwroot/fims.gbuilderchina.com/s',
    car: '/www/wwwroot/fims.gbuilderchina.com/car',
    stones: '/www/wwwroot/fims.gbuilderchina.com/stones'
  }
  const mapBuild = {
    tests: 'pnpm run build:test',
    prod: 'pnpm run build',
    car: 'pnpm run build:car',
    stones: 'pnpm run build:stone'
  }
  return {
    ...baseConfig,
    remoteFile: mapRemote[mode],
    buildCommand: mapBuild[mode]
  }
}
