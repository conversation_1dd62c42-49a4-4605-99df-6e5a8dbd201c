//资金档案
import { defHttp } from '/@/utils/http/axios'
enum Api {
  GetFundsLevel = '/stb/cd/get',
  GetFundsVoucherByName = '/stb/cd/recordlist'
}

export const getFundsLevel = (params?: { noCache?: number }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetFundsLevel, params })

export const getFundsVoucherByName = (params?: { page: number; pageSize: number; name?: string }) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetFundsVoucherByName, params })
