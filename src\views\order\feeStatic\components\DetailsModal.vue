<template>
  <BasicModal @register="registermodal" width="70%" :show-footer="false">
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicTable, useTable } from '/@/components/Table'

const propsData = ref({})

const [registermodal, { changeLoading }] = useModalInner(async (data) => {
  try {
    changeLoading(true)
    propsData.value = data
    const { details, columns } = data
    // 设置表格列配置
    if (columns) {
      setColumns(columns)
    }

    // 设置表格数据
    setTableData(details)
  } catch (err) {
    console.error(err)
  } finally {
    changeLoading(false)
  }
})

const baseTableConfig = {
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  canResize: false,
  columns: []
}

const [registerTable, { setTableData, setColumns }] = useTable(baseTableConfig)
</script>
