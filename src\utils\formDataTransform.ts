/**
 * 表单数据转换工具函数
 * 用于处理表单提交前的数据转换
 */

import type { Dayjs } from 'dayjs'

// 定义常用的数据转换接口
interface DateRange {
  0: Dayjs | null
  1: Dayjs | null
}

interface LabelValueItem {
  label: string
  value: any
  key?: string | number
}

interface TransformOptions {
  dateFormat?: string
  keepOriginalDate?: boolean
  logTransform?: boolean
}

/**
 * 转换表单数据
 * @param formData 原始表单数据
 * @param options 转换选项
 * @returns 转换后的数据
 */
export function transformFormData(formData: Record<string, any>, options: TransformOptions = {}) {
  const { dateFormat = 'YYYY-MM-DD', keepOriginalDate = false, logTransform = true } = options

  const transformed = { ...formData }

  if (logTransform) {
    console.log('🔄 开始转换表单数据:', formData)
  }

  // 处理日期范围字段
  Object.keys(transformed).forEach((key) => {
    const value = transformed[key]

    // 处理日期范围 (RangePicker)
    if (Array.isArray(value) && value.length === 2 && value[0]?.format) {
      const [startDate, endDate] = value as DateRange

      transformed[`${key}_start`] = startDate ? startDate.format(dateFormat) : ''
      transformed[`${key}_end`] = endDate ? endDate.format(dateFormat) : ''

      if (!keepOriginalDate) {
        delete transformed[key]
      }

      if (logTransform) {
        console.log(`📅 日期范围转换 [${key}]:`, {
          原始: value,
          开始: transformed[`${key}_start`],
          结束: transformed[`${key}_end`]
        })
      }
    }

    // 处理 labelInValue 格式的选择器数据
    if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'object' && 'value' in value[0]) {
      transformed[key] = value.map((item: LabelValueItem) => item.value)

      if (logTransform) {
        console.log(`🏷️ 标签值转换 [${key}]:`, {
          原始: value,
          转换后: transformed[key]
        })
      }
    }

    // 处理单个 labelInValue 对象
    if (value && typeof value === 'object' && 'value' in value && !Array.isArray(value)) {
      transformed[key] = (value as LabelValueItem).value

      if (logTransform) {
        console.log(`🏷️ 单个标签值转换 [${key}]:`, {
          原始: value,
          转换后: transformed[key]
        })
      }
    }
  })

  if (logTransform) {
    console.log('✅ 表单数据转换完成:', transformed)
  }

  return transformed
}

/**
 * 专门处理日期范围转换
 * @param dateRange 日期范围数组
 * @param format 日期格式
 * @returns 转换后的开始和结束日期
 */
export function transformDateRange(dateRange: DateRange, format = 'YYYY-MM-DD') {
  if (!Array.isArray(dateRange) || dateRange.length !== 2) {
    return { start_date: '', end_date: '' }
  }

  const [startDate, endDate] = dateRange

  return {
    start_date: startDate ? startDate.format(format) : '',
    end_date: endDate ? endDate.format(format) : ''
  }
}

/**
 * 处理部门选择器数据
 * @param deptData 部门数据
 * @returns 部门ID数组
 */
export function transformDeptIds(deptData: any[]): string[] {
  if (!Array.isArray(deptData)) {
    return []
  }

  return deptData.map((item) => {
    if (typeof item === 'object' && item.value) {
      return item.value
    }
    return item
  })
}

/**
 * 批量转换多个字段的日期范围
 * @param formData 表单数据
 * @param dateFields 需要转换的日期字段名数组
 * @param format 日期格式
 * @returns 转换后的数据
 */
export function transformMultipleDateRanges(formData: Record<string, any>, dateFields: string[], format = 'YYYY-MM-DD') {
  const transformed = { ...formData }

  dateFields.forEach((field) => {
    if (transformed[field] && Array.isArray(transformed[field])) {
      const { start_date, end_date } = transformDateRange(transformed[field], format)
      transformed[`${field}_start`] = start_date
      transformed[`${field}_end`] = end_date
      delete transformed[field]
    }
  })

  return transformed
}

/**
 * 表单数据预处理 - 常用的转换组合
 * @param formData 原始表单数据
 * @returns 处理后的数据
 */
export function preprocessFormData(formData: Record<string, any>) {
  console.log('🚀 开始预处理表单数据...')

  const processed = transformFormData(formData, {
    logTransform: true,
    keepOriginalDate: false
  })

  // 特殊处理常见字段名映射
  const fieldMappings = {
    date: ['start_date', 'end_date'],
    time_range: ['start_time', 'end_time'],
    created_at: ['created_start', 'created_end'],
    updated_at: ['updated_start', 'updated_end']
  }

  Object.entries(fieldMappings).forEach(([originalField, [startField, endField]]) => {
    if (processed[`${originalField}_start`] !== undefined) {
      processed[startField] = processed[`${originalField}_start`]
      processed[endField] = processed[`${originalField}_end`]
      delete processed[`${originalField}_start`]
      delete processed[`${originalField}_end`]
    }
  })

  console.log('✨ 表单数据预处理完成:', processed)
  return processed
}
