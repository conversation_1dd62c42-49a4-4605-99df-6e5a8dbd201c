<template>
  <BasicDrawer @register="registermodal" @ok="handleSubmit">
    <div v-if="types == 'add'">
      <BasicForm @register="registerform" />
    </div>
    <div v-else>
      <BasicTable @register="registerTable" @row-click="handleRowClick" />
    </div>
  </BasicDrawer>
</template>
<script setup lang="ts">
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { getDepartmentPermissionTree } from '/@/api/common/dept'
import { useERPStore } from '/@/store/modules/ERP'
import { transformDateRange } from '/@/utils/formDataTransform'
import { DepartmentProfitSimQueueDespatch, GetDepartmentProfitSimQueueResult } from '/@/api/newReportForm/divisionalprofit'
import { ref } from 'vue'
import { useTable, BasicTable } from '/@/components/Table'
import {
  changeDepartRecord,
  departmentType,
  transformTableData,
  transformTableDataToArray,
  extractDatesFromText
} from '../../divisionalprofit/datas/datas'
import { useProfitPotentialStore } from '/@/store/modules/profitPotential'
import { useGo } from '/@/hooks/web/usePage'
import { router } from '/@/router'

const erpStore = useERPStore()
const go = useGo(router)

const emit = defineEmits(['success'])
const types = ref()

const profitPotentialStore = useProfitPotentialStore()

const extractedDates = ref()

const [registermodal, { closeDrawer }] = useDrawerInner(async (data) => {
  types.value = data.type

  // 从 input_text 中提取日期信息
  if (data.record?.input_text) {
    extractedDates.value = extractDatesFromText(data.record.input_text)
  }

  if (data.type == 'detail') {
    setLoading(true)
    const { items } = await GetDepartmentProfitSimQueueResult({ id: data.record.id })
    handleAfterFetch(items)
    setLoading(false)
  } else {
    resetFields()
  }
})

const [registerform, { resetFields, validate }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  schemas: [
    {
      field: 'date',
      label: '日期',
      component: 'RangePicker',
      required: true,
      // required: !hasPermission([288]),
      defaultValue: erpStore.dateRangeArray,
      componentProps: {
        style: { width: '100%' },
        disabledDate: erpStore.disabledDate
      },
      colProps: { span: 24 }
    },
    {
      field: `dept_ids`,
      label: `部门`,
      component: 'ApiTreeSelect',
      // required: !hasPermission([92]),
      componentProps: ({ formActionType }) => ({
        resultField: 'items',
        immediate: false,
        lazyLoad: true,
        labelInValue: true,
        api: getDepartmentPermissionTree,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          treeCheckable: true,
          showCheckedStrategy: 'SHOW_ALL',
          treeDefaultExpandAll: true,
          maxTagCount: 5,
          showSearch: true,
          treeLine: {
            showLeafIcon: false
          },

          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        },
        onChange: () => {
          if (formActionType && formActionType.getFieldsValue().operation) {
            formActionType.setFieldsValue({
              operation: undefined
            })
          }
        }
      }),
      colProps: { span: 24 },
      required: ({ model }) => {
        return !model.operation
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    {
      field: `operation`,
      label: `部门(不筛选子部门)`,
      component: 'ApiTreeSelect',
      colProps: { span: 24 },
      componentProps: ({ formActionType }) => ({
        resultField: 'items',
        immediate: false,
        lazyLoad: true,
        maxTagCount: 5,
        labelInValue: true,
        api: getDepartmentPermissionTree,
        treeSelectProps: {
          fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
          multiple: true,
          treeDefaultExpandAll: true,
          showSearch: true,
          treeLine: {
            showLeafIcon: false
          },

          filterTreeNode: (search, item) => {
            if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
            return false
          }
        },
        onChange: () => {
          if (formActionType && formActionType.getFieldsValue().dept_ids) {
            formActionType.setFieldsValue({
              dept_ids: undefined
            })
          }
        }
      }),
      required: ({ model }) => {
        return !model.dept_ids
      },
      itemProps: {
        validateTrigger: 'blur'
      }
    }
  ],
  fieldMapToTime: [['date', ['start_date', 'end_date'], 'YYYY-MM-DD']]
})

async function handleSubmit() {
  try {
    const formData = await validate()

    // 只转换日期字段，其他保持原样
    const transformedData = { ...formData }

    // 处理日期范围转换
    if (formData.date && Array.isArray(formData.date)) {
      const { start_date, end_date } = transformDateRange(formData.date)
      transformedData.start_date = start_date
      transformedData.end_date = end_date
      transformedData.dept_ids = transformedData.dept_ids?.map((item) => item.value)
      delete transformedData.date
    }

    // 这里可以调用API提交数据
    await DepartmentProfitSimQueueDespatch(transformedData)
    emit('success')
    closeDrawer()
  } catch (e) {
    console.log('❌ 表单验证失败:', e)
  }
}

const [registerTable, { setColumns, setTableData, setLoading }] = useTable({
  showIndexColumn: false,
  isTreeTable: true
})

function handleAfterFetch(data) {
  const newcolumns = transformTableDataToArray(data)
  const newdata = transformTableData(data)
  setColumns(newcolumns)
  setTableData(newdata)
}

function handleRowClick() {
  const transferData = {
    no_cache: '0',
    startDate: extractedDates.value[0],
    endDate: extractedDates.value[1],
    department_else: void 0,
    department: changeDepartRecord.value
  }

  // 先将参数存储到store中（像你选择的代码一样）
  profitPotentialStore.setToProductOperationParams(transferData)
  switch (departmentType.value) {
    case '产品部现金流利润':
      go('/operationdataanalysis')
      break
    case '业务部现金流利润':
      go('/operationdataanalysisdept')
      break
    case '产品部营业费用':
    case '业务部营业费用':
      go('/reportForm/feeStaticDept')
      break
    default:
      console.log('其他部门类型，暂未配置跳转')
      break
  }
}
</script>
