import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetSettingList = '/setting/getSettingList',
  GetSettingValue = '/setting/getSettingValue',
  SetSettingValue = '/setting/setSettingValue',
  DeleteSettingValue = '/setting/deleteSettingValue'
}
export function getSettingList(params) {
  return defHttp.get({ url: Api.GetSettingList, params })
}

export function getSettingValue(params) {
  return defHttp.get({ url: Api.GetSettingValue, params })
}

export function setSettingValue(params) {
  return defHttp.post({ url: Api.SetSettingValue, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
}

export function deleteSettingValue(params) {
  return defHttp.post({ url: Api.DeleteSettingValue, params })
}
