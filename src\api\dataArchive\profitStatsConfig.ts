import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetProfitConfig = '/profitconfig/getProfitConfigTree',
  UpdateProfitConfig = '/profitconfig/upsertProfitConfig',
  DeleteProfitConfig = '/profitconfig/deleteProfitConfig'
}
export function getProfitConfigList(params) {
  return defHttp.get({ url: Api.GetProfitConfig, params })
}

export function updateProfitConfigConfig(data) {
  return defHttp.post({ url: Api.UpdateProfitConfig, data })
}

export function deleteProfitConfigConfig(data) {
  return defHttp.post({ url: Api.DeleteProfitConfig, data })
}

export async function getCategoryLike(params) {
  const items = await getProfitConfigList(params)
  const result = extractItems(items)
  return result
}

function extractItems(data) {
  const result: any = []

  function traverse(items) {
    items.forEach((item) => {
      if (!item.children) {
        result.push({ label: item.name, value: item.name })
      } else {
        traverse(item.children)
      }
    })
  }

  traverse(data)
  return result
}

function extractItemsStringArray(data) {
  const result: any = []

  function traverse(items) {
    items.forEach((item) => {
      if (!item.children) {
        result.push(item.name)
      } else {
        traverse(item.children)
      }
    })
  }

  traverse(data)
  return result
}

export async function getCategoryLikeStringArray(params) {
  const items = await getProfitConfigList(params)
  const result = extractItemsStringArray(items)
  return result
}
