<template>
  <div>
    <BasicTable @register="registerTable" :loading="loading">
      <template #form-advanceBefore v-if="hasPermission([312])">
        <a-button type="primary" @click="handleExport" :loading="exportLoading" :disabled="exportLoading">
          <download-outlined />
          根据搜索条件导出文件
        </a-button>
      </template>
    </BasicTable>
    <!-- <DetailsModal @register="registerModal" /> -->
  </div>
</template>
<script lang="ts" setup name="PAGE_303">
import { useTable, BasicTable } from '/@/components/Table'
import { getColumns, schema, searchInfoCategory } from '../feeStatic/datas/data'
import dayjs from 'dayjs'
import { getexportFeeStatic, getFeeStatic } from '/@/api/order/feeStatic'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
// import { useModal } from '/@/components/Modal'
// import DetailsModal from '../feeStatic/components/DetailsModal.vue'
import { downloadByData } from '/@/utils/file/download'
import { usePermission } from '/@/hooks/web/usePermission'

import { cloneDeep } from 'lodash-es'
const route = useRoute()

// const [registerModal, { openModal, setModalProps }] = useModal()
const loading = ref(true)
// getexportFeeStatic
const exportLoading = ref(false)
const { hasPermission } = usePermission()

const [registerTable, { setColumns, reload, setLoading, getForm }] = useTable({
  searchInfo: {
    category1: '营业费用',
    group_by: 'category'
  },
  title: '费用汇总(按科目)',
  api: getFeeStatic,
  showTableSetting: true,
  columns: [],
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  pagination: false,
  useSearchForm: true,
  immediate: false,
  formConfig: {
    labelWidth: 150,
    schemas: cloneDeep(schema),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  beforeFetch: handleBeforeFetch
})

function handleBeforeFetch(params) {
  searchInfoCategory.value = {
    ...params,
    year: dayjs(params.year).format('YYYY')
  }
  return searchInfoCategory.value
}

onMounted(async () => {
  // 初始化表格
  await setColumns(getColumns(route.path))

  reload()
})

async function handleExport() {
  try {
    setLoading(true)
    exportLoading.value = true

    const cloneParams = await getForm().getFieldsValue()

    const params = await handleBeforeFetch(cloneParams)

    const res = await getexportFeeStatic({ ...params, category1: '营业费用', group_by: 'category' })

    downloadByData(res as any, `FIMS-费用汇总凭证(科目)列表导出文件-${Date.now()}.xlsx`)
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
    exportLoading.value = false
  }
}
</script>
