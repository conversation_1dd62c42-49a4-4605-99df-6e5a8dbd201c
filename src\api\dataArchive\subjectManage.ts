//部门科目余额
import { defHttp } from '/@/utils/http/axios'
import { SubjectParams } from '/@/api/dataArchive/model/subjectManage'
enum Api {
  GetSubjectList = '/acc/getAccountManageTree',
  UpdateSubject = '/acc/upsertAccountManage',
  DeleteAccountManage = '/acc/deleteAccountManage'
}

export const getSubjectList = (params?: SubjectParams) =>
  defHttp.get<{
    header: string[]
    data: Recordable[]
  }>({ url: Api.GetSubjectList, params })

export const updateSubject = (params) => defHttp.post({ url: Api.UpdateSubject, params })

//删除
export const deleteAccountManage = (params?: SubjectParams) =>
  defHttp.get({ url: Api.DeleteAccountManage, params }, { errorMessageMode: 'message' })
