import { defineStore } from 'pinia'

export const useProfitPotentialStore = defineStore('profitPotential', {
  state: () => ({
    toProductOperationParams: undefined,
    toOrderListStbParams: undefined,
    toProfitPotentialConfigParams: undefined,
    tofeestaticParams: undefined
  }),
  actions: {
    setToProductOperationParams(value) {
      this.toProductOperationParams = value
    },
    setToOrderListStbParams(value) {
      this.toOrderListStbParams = value
    },
    setToProfitPotentialConfigParams(value) {
      this.toProfitPotentialConfigParams = value
    },
    setTofeestaticParams(value) {
      this.tofeestaticParams = value
    }
  }
})
