const utils = require('./utils.ts')
const getConfig = require('./config.ts')
// shelljs和json-diff是全局下载的，node_modules会被删除，所以无法下载到项目中
const shell = require('shelljs')
const jsondiff = require('json-diff')
const path = require('path')

const fs = require('fs')
const fsp = require('fs').promises

// 备份的package.json文件名为 package.json.bak
const cachePackageJson = path.resolve(__dirname, './cache.package.json')

// 获取命令行参数
const args = process.argv.slice(2)

const params = utils.formatParams(args)

const config = getConfig(params.mode)

let totalFileCount = 0 // 本地dist文件夹中总文件数量
let totalUploadFiles = 0 // 已成功上传到远端服务器上的文件数量
// 输出参数
console.log('传递的参数:', params)

// 统计本地dist文件夹中有多少个文件（用于计算文件上传进度）
function foldFileCount(folderPath) {
  let count = 0
  const files = fs.readdirSync(folderPath) // 读取文件夹
  for (const file of files) {
    // 遍历
    const filePath = path.join(folderPath, file)
    const stats = fs.statSync(filePath)
    if (stats.isFile()) {
      // 文件就+1
      count = count + 1
    } else if (stats.isDirectory()) {
      // 文件夹就递归加
      count = count + foldFileCount(filePath)
    }
  }
  return count
}

async function connectSSH() {
  const Client = require('ssh2-sftp-client')
  const sftp = new Client()
  const { host, port, username, privateKey, remoteFile } = config
  // const originDistFile = '/erp.gbuilderchina.com/dist'
  try {
    await sftp.connect({
      host,
      port,
      username,
      privateKey
    })

    sftp.on('upload', (info) => {
      if (!info.source || !info.destination) return
      totalUploadFiles += 1
      process.stdout.write(
        `Uploading: ${((totalUploadFiles / totalFileCount) * 100).toFixed(2)}% (${totalUploadFiles} / ${totalFileCount}) \r`
      )
    })

    const hasRemoteFile = await sftp.exists(remoteFile)
    if (hasRemoteFile) {
      await sftp.rmdir(remoteFile, true)
      console.log('已删除存在的远程文件夹')
    }
    // const hasDistFile = await sftp.exists(originDistFile)
    // if (hasDistFile) {
    //   await sftp.rmdir(originDistFile, true)
    // }
    await sftp.mkdir(remoteFile)
    console.log('\n上传文件成功！')
    await sftp.uploadDir(path.resolve(__dirname, '../dist'), remoteFile)
    console.log('上传文件成功！')
    // await sftp.rename('/erp.gbuilderchina.com/dist', remoteFile)
  } catch (err) {
    console.log(err)
  } finally {
    console.log('服务器连接结束')
    await sftp.end()
  }
}

async function compileDist() {
  const folderExists = shell.test('-d', 'node_modules')

  // node_modules文件夹不存在，执行pnpm i
  if (!folderExists) {
    // console.log('正在删除node_modules文件夹···')
    // shell.rm('-rf', 'node_modules')
    console.log('Reinstalling dependencies using pnpm...')
    shell.exec('pnpm i')
  }

  // 重新下载依赖
  // const installResult = shell.exec('pnpm i')
  // if (installResult.code !== 0) {
  //   console.error('install Error', installResult)
  //   process.exit(1)
  // }

  // build执行
  const { buildCommand } = config
  const commandArr = buildCommand.split(' ')
  const { spawn } = require('child_process')
  const buildProcess = spawn(`${commandArr[0]}.cmd`, commandArr.slice(1), { stdio: 'inherit' })

  buildProcess.on('error', (err) => {
    console.error('Failed to start subprocess:', err)
  })

  buildProcess.on('close', async (code) => {
    console.log(`Build process exited with code ${code}`)
    if (code === 0) {
      // 开始上传
      totalFileCount = foldFileCount(path.resolve(__dirname, '../dist'))
      await connectSSH()
    }
  })
  // 开始上传
  // await connectSSH()
}

async function compareJson() {
  const packageJsonPath = path.resolve(__dirname, '../package.json')
  //保存当前的 package.json 内容
  const currentPackageJson = await fsp.readFile(packageJsonPath, 'utf8')

  let previousPackageJson = null
  if (fs.existsSync(cachePackageJson)) {
    previousPackageJson = await fsp.readFile(cachePackageJson, 'utf8')
  }

  // 比较新旧两个json文件
  const oldPackageJson = previousPackageJson ? JSON.parse(previousPackageJson) : '{}'
  const newPackageJson = JSON.parse(currentPackageJson)
  const diff = jsondiff.diff(
    JSON.stringify({ devDependencies: oldPackageJson.devDependencies, dependencies: oldPackageJson.dependencies }),
    JSON.stringify({ devDependencies: newPackageJson.devDependencies, dependencies: newPackageJson.dependencies })
  )

  // 如果 package.json 发生变化，删除 node_modules 并安装依赖
  if (diff) {
    console.log('Detected changes in package.json. Removing node_modules...')
    shell.rm('-rf', 'node_modules')
    shell.rm('-rf', cachePackageJson)
    shell.cp(packageJsonPath, cachePackageJson)
    // console.log('Reinstalling dependencies using pnpm...')
    // shell.exec('pnpm i')
  } else {
    console.log('No changes detected in package.json. Skipping node_modules removal and installation.')
  }
  compileDist()
}

function getCurrentGitBranch(callback) {
  // 空数组为不限制分支
  const mapBranch = {
    prod: ['master'],
    car: ['master'],
    tests: ['tests'],
    stones: ['master']
  }
  const branch = shell.exec('git rev-parse --abbrev-ref HEAD')
  // console.log('branch', branch)
  // branch.stdout带有/n
  if (!branch.stdout.replace(/\n/, '').includes(mapBranch[params.mode])) {
    console.log('分支不对应，不允许上传')
    return
  }
  const isLatest = shell.exec('git pull')
  if (isLatest.code !== 0 || !isLatest.stdout.includes('Already up to date.')) {
    console.log('git pull执行失败：', isLatest.stderr)
    return
  }
  callback()
  // compareJson()
}

async function runTask() {
  getCurrentGitBranch(compareJson)
  // compareJson()
  // await compileDist()
}

runTask()
