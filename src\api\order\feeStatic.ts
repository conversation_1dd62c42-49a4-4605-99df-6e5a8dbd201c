import { defHttp } from '/@/utils/http/axios'

enum Api {
  getFeeStatic = '/fee/getFeeStatic',
  getFeeStaticDetail = '/fee/getFeeStatic',
  getexportFeeStatic = '/fee/exportFeeStatic'
}

export const getFeeStatic = (params?) => defHttp.get({ url: Api.getFeeStatic, params })
export const getFeeStaticDetail = (params?) => defHttp.get({ url: Api.getFeeStaticDetail, params })
export const getexportFeeStatic = (params) =>
  defHttp.get(
    { url: Api.getexportFeeStatic, responseType: 'blob', params },
    { isTransformResponse: false, errorMessageMode: 'message', successMessageMode: 'message' }
  )
