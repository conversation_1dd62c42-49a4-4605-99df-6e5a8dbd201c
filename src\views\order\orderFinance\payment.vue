<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-advanceBefore>
        <a-button type="primary" @click="handleExport" :loading="exportLoading" :disabled="exportLoading">
          <download-outlined />
          根据搜索条件导出文件
        </a-button>
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="PAGE_302">
import { useTable, BasicTable } from '/@/components/Table'
import { getSchema, paymentColumns } from './datas/data'
import { nextTick, ref } from 'vue'
import { downloadByData } from '/@/utils/file/download'
import { GetExportOrderPayment, getOrderPayment } from '/@/api/order/payment'

const exportLoading = ref(false)

const [registerTable, { getForm, getRawDataSource, setLoading }] = useTable({
  title: '订单支出',
  showTableSetting: true,
  columns: paymentColumns,
  api: getOrderPayment,
  rowKey: 'id',
  size: 'small',
  bordered: true,
  showIndexColumn: false,
  useSearchForm: true,
  immediate: false,
  formConfig: {
    labelWidth: 150,
    schemas: getSchema('/reportForm/payment'),
    autoSubmitOnEnter: true,
    baseColProps: { span: 8 },
    fieldMapToTime: [
      ['submited_at', ['submited_at_start', 'submited_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['audit_at', ['audit_at_start', 'audit_at_end'], ['YYYY-MM-DD', 'YYYY-MM-DD']],
      ['date', ['start_date', 'end_date'], ['YYYY-MM-DD', 'YYYY-MM-DD']]
    ]
  },
  showSummary: true,
  summaryFunc: () => {
    nextTick()
    const rawDataSource = getRawDataSource()
    return [
      {
        dept_name: '汇总',
        amount: rawDataSource.total_amount
      }
    ]
  }
})

async function handleExport() {
  try {
    setLoading(true)
    exportLoading.value = true

    const cloneParams = await getForm().getFieldsValue()

    const res = await GetExportOrderPayment(cloneParams)

    downloadByData(res as any, `FIMS-订单支出列表导出文件-${Date.now()}.xlsx`)
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
    exportLoading.value = false
  }
}
</script>
