import { RcFile } from 'ant-design-vue/lib/vc-upload/interface'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  CommonFileUpload = '/shareholding/uploadFile'
}

export interface BasicFormDataResult {
  path: string
}

// 文件上传
export const commonFileUpload = (file: string | Blob | RcFile, path?: string, curFile?: any) =>
  defHttp.post<BasicFormDataResult>({
    url: Api.CommonFileUpload,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: { file, path },
    onUploadProgress: (progressEvent) => {
      if (curFile) {
        curFile.percent = Math.round(progressEvent.progress * 100)
      }
    }
  })
