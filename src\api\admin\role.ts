import { RoleItem } from './model/roleModel'
import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel'
import { defHttp } from '/@/utils/http/axios'

enum Api {
  GetRolePageList = '/role/getPageListMoGeGe',
  // GetRolePageList = '/role/getPageList',
  GetAllRoleList = '/role/getList',
  CreateRoleItem = '/role/create',
  UpdateRoleItem = '/role/update',
  GetRoleItemDetail = '/role/detail',
  DeleteRoleItem = '/role/delete'
}
export const getRoleListByPage = (params?: {} & BasicPageParams) =>
  defHttp.get<BasicFetchResult<RoleItem>>({ url: Api.GetRolePageList, params })

export const getAllRoleList = (params: { id: number }) => defHttp.get<RoleItem[]>({ url: Api.GetAllRoleList, params })

export const createRoleItem = (params: RoleItem) => defHttp.post({ url: Api.CreateRoleItem, params }, { successMessageMode: 'message' })

export const updateRoleItem = (params: RoleItem) => defHttp.post({ url: Api.UpdateRoleItem, params }, { successMessageMode: 'message' })

export const setRoleItemStatus = (id: number, status: number) =>
  defHttp.post({ url: Api.UpdateRoleItem, params: { id, status } }, { successMessageMode: 'message' })

export const GetRoleDetail = (params: { id: number }) => defHttp.get<RoleItem>({ url: Api.GetRoleItemDetail, params })

export const deleteRoleItem = (params?: { id: number }) =>
  defHttp.get<{ id: number }>({ url: Api.DeleteRoleItem, params }, { successMessageMode: 'message' })
