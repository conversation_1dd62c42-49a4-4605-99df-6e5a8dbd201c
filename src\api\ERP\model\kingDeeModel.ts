export interface RecordItem {
  date: string
  act_date: string
  cert_number: string
  entry_number: string
  description: string
  account_code: string
  account_name: string
  department: string
  personnel: string
  oga_amount: number
  dbr_amount: number
  cdr_amount: number
  maker_bills: string
  audit: string
  carry: string
  check: string
  cashier: string
  handle: string
  comment: string
  payment: string
  pay_number: string
  quantity: number
  unit_price: number
  reference: string
  business_date: string
  transaction: string
  attach_number: number
  serial_number: number
  system_model: string
  business_desc: string
  financial: string
}
